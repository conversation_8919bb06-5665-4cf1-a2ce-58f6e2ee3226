import {
  ImageFile,
  ConversionSettings,
  ConvertedFile,
  ProcessingProgress,
  ProcessingStatus,
  WorkerMessageType,
  ConvertImageMessage,
  ConversionProgressMessage,
  ConversionCompleteMessage,
  ConversionErrorMessage,
  ProcessingError
} from '../types';
import { APP_CONFIG } from '../constants';
import { generateFileId } from './fileUtils';

interface WorkerTask {
  id: string;
  imageFile: ImageFile;
  settings: ConversionSettings;
  resolve: (result: ConvertedFile) => void;
  reject: (error: ProcessingError) => void;
  onProgress?: (progress: ProcessingProgress) => void;
  startTime: number;
}

interface WorkerInstance {
  worker: Worker;
  busy: boolean;
  currentTask?: WorkerTask;
}

export class WorkerManager {
  private workers: WorkerInstance[] = [];
  private taskQueue: WorkerTask[] = [];
  private activeTasks = new Map<string, WorkerTask>();
  private workerUrl: string;

  constructor() {
    this.workerUrl = new URL('../workers/imageWorker.ts', import.meta.url).href;
    this.initializeWorkers();
  }

  private initializeWorkers(): void {
    const poolSize = Math.min(APP_CONFIG.workerPoolSize, navigator.hardwareConcurrency || 4);
    
    for (let i = 0; i < poolSize; i++) {
      this.createWorker();
    }
  }

  private createWorker(): void {
    try {
      const worker = new Worker(this.workerUrl, { type: 'module' });
      
      const workerInstance: WorkerInstance = {
        worker,
        busy: false
      };

      worker.addEventListener('message', (event) => {
        this.handleWorkerMessage(workerInstance, event.data);
      });

      worker.addEventListener('error', (error) => {
        this.handleWorkerError(workerInstance, error);
      });

      this.workers.push(workerInstance);
    } catch (error) {
      console.error('Failed to create worker:', error);
    }
  }

  private handleWorkerMessage(
    workerInstance: WorkerInstance,
    message: ConversionProgressMessage | ConversionCompleteMessage | ConversionErrorMessage
  ): void {
    const task = this.activeTasks.get(message.id);
    if (!task) {
      console.warn('Received message for unknown task:', message.id);
      return;
    }

    switch (message.type) {
      case WorkerMessageType.CONVERSION_PROGRESS:
        this.handleProgressMessage(task, message as ConversionProgressMessage);
        break;

      case WorkerMessageType.CONVERSION_COMPLETE:
        this.handleCompleteMessage(workerInstance, task, message as ConversionCompleteMessage);
        break;

      case WorkerMessageType.CONVERSION_ERROR:
        this.handleErrorMessage(workerInstance, task, message as ConversionErrorMessage);
        break;
    }
  }

  private handleProgressMessage(task: WorkerTask, message: ConversionProgressMessage): void {
    if (task.onProgress) {
      const progress: ProcessingProgress = {
        fileId: task.imageFile.id,
        status: ProcessingStatus.PROCESSING,
        progress: message.payload.progress,
        message: message.payload.message,
        startTime: task.startTime
      };
      task.onProgress(progress);
    }
  }

  private handleCompleteMessage(
    workerInstance: WorkerInstance,
    task: WorkerTask,
    message: ConversionCompleteMessage
  ): void {
    const { blob, fileName, conversionTime } = message.payload;
    
    const result: ConvertedFile = {
      id: generateFileId(),
      originalFile: task.imageFile,
      convertedBlob: blob,
      convertedName: fileName,
      convertedSize: blob.size,
      convertedFormat: task.settings.targetFormat,
      conversionTime,
      compressionRatio: task.imageFile.size / blob.size
    };

    // Send final progress update
    if (task.onProgress) {
      const progress: ProcessingProgress = {
        fileId: task.imageFile.id,
        status: ProcessingStatus.COMPLETED,
        progress: 100,
        startTime: task.startTime,
        endTime: Date.now()
      };
      task.onProgress(progress);
    }

    task.resolve(result);
    this.finishTask(workerInstance, task.id);
  }

  private handleErrorMessage(
    workerInstance: WorkerInstance,
    task: WorkerTask,
    message: ConversionErrorMessage
  ): void {
    // Send error progress update
    if (task.onProgress) {
      const progress: ProcessingProgress = {
        fileId: task.imageFile.id,
        status: ProcessingStatus.ERROR,
        progress: 0,
        error: message.payload.error.message,
        startTime: task.startTime,
        endTime: Date.now()
      };
      task.onProgress(progress);
    }

    task.reject(message.payload.error);
    this.finishTask(workerInstance, task.id);
  }

  private handleWorkerError(workerInstance: WorkerInstance, error: ErrorEvent): void {
    console.error('Worker error:', error);
    
    if (workerInstance.currentTask) {
      const task = workerInstance.currentTask;
      const processingError: ProcessingError = {
        type: 'processing_error' as any,
        message: 'Worker encountered an error',
        fileId: task.imageFile.id,
        fileName: task.imageFile.name,
        details: error.message,
        timestamp: Date.now()
      };
      
      task.reject(processingError);
      this.finishTask(workerInstance, task.id);
    }

    // Recreate the worker
    this.recreateWorker(workerInstance);
  }

  private finishTask(workerInstance: WorkerInstance, taskId: string): void {
    workerInstance.busy = false;
    workerInstance.currentTask = undefined;
    this.activeTasks.delete(taskId);
    
    // Process next task in queue
    this.processNextTask();
  }

  private recreateWorker(workerInstance: WorkerInstance): void {
    // Terminate the problematic worker
    workerInstance.worker.terminate();
    
    // Find and replace the worker in the array
    const index = this.workers.indexOf(workerInstance);
    if (index !== -1) {
      this.workers.splice(index, 1);
      this.createWorker();
    }
  }

  private processNextTask(): void {
    if (this.taskQueue.length === 0) return;

    const availableWorker = this.workers.find(w => !w.busy);
    if (!availableWorker) return;

    const task = this.taskQueue.shift()!;
    this.assignTaskToWorker(availableWorker, task);
  }

  private async assignTaskToWorker(workerInstance: WorkerInstance, task: WorkerTask): Promise<void> {
    workerInstance.busy = true;
    workerInstance.currentTask = task;
    this.activeTasks.set(task.id, task);

    try {
      // Convert file to ImageData
      const imageData = await this.fileToImageData(task.imageFile.file);
      
      const message: ConvertImageMessage = {
        type: WorkerMessageType.CONVERT_IMAGE,
        id: task.id,
        payload: {
          imageData,
          settings: task.settings,
          fileName: task.imageFile.name
        }
      };

      workerInstance.worker.postMessage(message);
    } catch (error) {
      const processingError: ProcessingError = {
        type: 'processing_error' as any,
        message: 'Failed to prepare image for processing',
        fileId: task.imageFile.id,
        fileName: task.imageFile.name,
        details: error,
        timestamp: Date.now()
      };
      
      task.reject(processingError);
      this.finishTask(workerInstance, task.id);
    }
  }

  private fileToImageData(file: File): Promise<ImageData> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      img.onload = () => {
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;
        ctx.drawImage(img, 0, 0);
        
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        URL.revokeObjectURL(img.src);
        resolve(imageData);
      };

      img.onerror = () => {
        URL.revokeObjectURL(img.src);
        reject(new Error('Failed to load image'));
      };

      img.src = URL.createObjectURL(file);
    });
  }

  public convertImage(
    imageFile: ImageFile,
    settings: ConversionSettings,
    onProgress?: (progress: ProcessingProgress) => void
  ): Promise<ConvertedFile> {
    return new Promise((resolve, reject) => {
      const task: WorkerTask = {
        id: generateFileId(),
        imageFile,
        settings,
        resolve,
        reject,
        onProgress,
        startTime: Date.now()
      };

      // Try to assign to available worker immediately
      const availableWorker = this.workers.find(w => !w.busy);
      if (availableWorker) {
        this.assignTaskToWorker(availableWorker, task);
      } else {
        // Add to queue
        this.taskQueue.push(task);
      }
    });
  }

  public async convertBatch(
    imageFiles: ImageFile[],
    settings: ConversionSettings,
    onProgress?: (progress: ProcessingProgress) => void
  ): Promise<ConvertedFile[]> {
    const promises = imageFiles.map(imageFile => 
      this.convertImage(imageFile, settings, onProgress)
    );

    return Promise.all(promises);
  }

  public getQueueLength(): number {
    return this.taskQueue.length;
  }

  public getActiveTasksCount(): number {
    return this.activeTasks.size;
  }

  public destroy(): void {
    // Clear queue
    this.taskQueue.length = 0;
    
    // Terminate all workers
    this.workers.forEach(workerInstance => {
      workerInstance.worker.terminate();
    });
    
    this.workers.length = 0;
    this.activeTasks.clear();
  }
}

// Singleton instance
let workerManagerInstance: WorkerManager | null = null;

export function getWorkerManager(): WorkerManager {
  if (!workerManagerInstance) {
    workerManagerInstance = new WorkerManager();
  }
  return workerManagerInstance;
}
