import { ImageFile, ProcessingError } from '../types';
import { validateFiles, fileToImageFile } from '../utils/fileUtils';

export interface FileUploadOptions {
  onFilesSelected: (files: ImageFile[]) => void;
  onError: (errors: ProcessingError[]) => void;
  onDragStateChange: (isDragging: boolean) => void;
  multiple?: boolean;
  accept?: string;
}

export class FileUpload {
  private dropZone: HTMLElement;
  private fileInput: HTMLInputElement;
  private options: FileUploadOptions;
  private isDragging = false;
  private dragCounter = 0;

  constructor(dropZone: HTMLElement, options: FileUploadOptions) {
    this.dropZone = dropZone;
    this.options = options;
    this.fileInput = this.createFileInput();
    this.init();
  }

  private createFileInput(): HTMLInputElement {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = this.options.multiple ?? true;
    input.accept = this.options.accept ?? 'image/*';
    input.style.display = 'none';
    document.body.appendChild(input);
    return input;
  }

  private init(): void {
    this.setupDropZone();
    this.setupFileInput();
  }

  private setupDropZone(): void {
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, this.preventDefaults.bind(this), false);
      document.body.addEventListener(eventName, this.preventDefaults.bind(this), false);
    });

    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, this.handleDragEnter.bind(this), false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, this.handleDragLeave.bind(this), false);
    });

    // Handle dropped files
    this.dropZone.addEventListener('drop', this.handleDrop.bind(this), false);

    // Handle click to open file dialog
    this.dropZone.addEventListener('click', this.handleClick.bind(this), false);
  }

  private setupFileInput(): void {
    this.fileInput.addEventListener('change', this.handleFileInputChange.bind(this), false);
  }

  private preventDefaults(e: Event): void {
    e.preventDefault();
    e.stopPropagation();
  }

  private handleDragEnter(e: Event): void {
    const dragEvent = e as DragEvent;
    this.dragCounter++;
    if (dragEvent.dataTransfer?.items && this.hasImageFiles(dragEvent.dataTransfer.items)) {
      this.setDragState(true);
    }
  }

  private handleDragLeave(_e: Event): void {
    this.dragCounter--;
    if (this.dragCounter === 0) {
      this.setDragState(false);
    }
  }

  private handleDrop(e: Event): void {
    const dragEvent = e as DragEvent;
    this.dragCounter = 0;
    this.setDragState(false);

    const files = dragEvent.dataTransfer?.files;
    if (files) {
      this.processFiles(Array.from(files));
    }
  }

  private handleClick(): void {
    this.fileInput.click();
  }

  private handleFileInputChange(): void {
    const files = this.fileInput.files;
    if (files) {
      this.processFiles(Array.from(files));
    }
    // Reset input value to allow selecting the same files again
    this.fileInput.value = '';
  }

  private hasImageFiles(items: DataTransferItemList): boolean {
    return Array.from(items).some(item => 
      item.kind === 'file' && item.type.startsWith('image/')
    );
  }

  private setDragState(isDragging: boolean): void {
    if (this.isDragging !== isDragging) {
      this.isDragging = isDragging;
      this.options.onDragStateChange(isDragging);
      
      // Update visual state
      if (isDragging) {
        this.dropZone.classList.add('drag-over');
      } else {
        this.dropZone.classList.remove('drag-over');
      }
    }
  }

  private async processFiles(files: File[]): Promise<void> {
    if (files.length === 0) return;

    // Validate files
    const { validFiles, errors } = validateFiles(files);

    // Report errors if any
    if (errors.length > 0) {
      this.options.onError(errors);
    }

    // Process valid files
    if (validFiles.length > 0) {
      try {
        const imageFiles = await Promise.all(
          validFiles.map(file => fileToImageFile(file))
        );
        this.options.onFilesSelected(imageFiles);
      } catch (error) {
        this.options.onError([{
          type: 'processing_error' as any,
          message: 'Failed to process selected files',
          timestamp: Date.now(),
          details: error
        }]);
      }
    }
  }

  public destroy(): void {
    // Remove event listeners
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      this.dropZone.removeEventListener(eventName, this.preventDefaults.bind(this), false);
      document.body.removeEventListener(eventName, this.preventDefaults.bind(this), false);
    });

    ['dragenter', 'dragover'].forEach(eventName => {
      this.dropZone.removeEventListener(eventName, this.handleDragEnter.bind(this), false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      this.dropZone.removeEventListener(eventName, this.handleDragLeave.bind(this), false);
    });

    this.dropZone.removeEventListener('drop', this.handleDrop.bind(this), false);
    this.dropZone.removeEventListener('click', this.handleClick.bind(this), false);
    this.fileInput.removeEventListener('change', this.handleFileInputChange.bind(this), false);

    // Remove file input from DOM
    if (this.fileInput.parentNode) {
      this.fileInput.parentNode.removeChild(this.fileInput);
    }
  }

  public openFileDialog(): void {
    this.fileInput.click();
  }
}

// Utility function to create file upload instance
export function createFileUpload(
  dropZoneSelector: string,
  options: FileUploadOptions
): FileUpload | null {
  const dropZone = document.querySelector(dropZoneSelector) as HTMLElement;
  if (!dropZone) {
    console.error(`Drop zone element not found: ${dropZoneSelector}`);
    return null;
  }

  return new FileUpload(dropZone, options);
}
