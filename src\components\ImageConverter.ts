import {
  ImageFile,
  ConversionSettings,
  ConvertedFile,
  BatchJob,
  ProcessingProgress,
  ProcessingStatus,
  ImageFormat,
  ProcessingError
} from '../types';
import { FileUpload } from './FileUpload';
import { getBatchProcessor } from '../utils/batchProcessor';
import { getDownloadManager } from '../utils/downloadManager';
import { getPerformanceMonitor } from '../utils/performanceMonitor';
import { toastManager, createProgressBar, createStatusBadge, formatFileSize } from '../utils/uiUtils';
import { FORMAT_NAMES, DEFAULT_QUALITY } from '../constants';

export class ImageConverter {
  private container: HTMLElement;
  private fileUpload: FileUpload | null = null;
  private batchProcessor = getBatchProcessor({
    onJobProgress: this.handleJobProgress.bind(this),
    onFileProgress: this.handleFileProgress.bind(this),
    onJobComplete: this.handleJobComplete.bind(this),
    onJobError: this.handleJobError.bind(this)
  });
  private downloadManager = getDownloadManager();
  private performanceMonitor = getPerformanceMonitor();

  private selectedFiles: ImageFile[] = [];
  private currentJob: BatchJob | null = null;
  private convertedResults: ConvertedFile[] = [];
  private conversionSettings: ConversionSettings = {
    targetFormat: ImageFormat.JPEG,
    quality: DEFAULT_QUALITY.jpeg,
    maintainDimensions: true
  };

  constructor(containerId: string) {
    const container = document.getElementById(containerId);
    if (!container) {
      throw new Error(`Container element with id "${containerId}" not found`);
    }
    this.container = container;
    this.init();
  }

  private init(): void {
    this.render();
    this.setupFileUpload();
    this.setupEventListeners();
  }

  private render(): void {
    this.container.innerHTML = `
      <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Image Converter</h1>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Convert images between JPEG, PNG, WebP, GIF, and BMP formats</p>
              </div>
              <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                </svg>
              </button>
            </div>
          </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <!-- Upload Section -->
          <div class="mb-8">
            <div id="drop-zone" class="drop-zone">
              <div class="space-y-4">
                <div class="mx-auto w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                  <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">Drop images here</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">or <button class="text-primary-600 hover:text-primary-500 font-medium">browse files</button></p>
                  <p class="text-xs text-gray-400 dark:text-gray-500 mt-2">Supports JPEG, PNG, WebP, GIF, BMP • Max 50MB per file • Up to 20 files</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Settings Section -->
          <div id="settings-section" class="mb-8 hidden">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Conversion Settings</h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label for="target-format" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Target Format</label>
                  <select id="target-format" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <option value="${ImageFormat.JPEG}">JPEG</option>
                    <option value="${ImageFormat.PNG}">PNG</option>
                    <option value="${ImageFormat.WEBP}">WebP</option>
                    <option value="${ImageFormat.GIF}">GIF</option>
                    <option value="${ImageFormat.BMP}">BMP</option>
                  </select>
                </div>
                <div>
                  <label for="quality-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quality: <span id="quality-value">92%</span></label>
                  <input type="range" id="quality-slider" min="10" max="100" value="92" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider">
                </div>
                <div class="flex items-end">
                  <button id="convert-btn" class="btn-primary w-full" disabled>
                    Convert Images
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Files Section -->
          <div id="files-section" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">Selected Files</h3>
                  <button id="clear-files" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">Clear All</button>
                </div>
              </div>
              <div id="files-list" class="divide-y divide-gray-200 dark:divide-gray-700"></div>
            </div>
          </div>

          <!-- Results Section -->
          <div id="results-section" class="hidden mt-8">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">Conversion Results</h3>
                  <div class="flex space-x-2">
                    <button id="download-all" class="btn-secondary text-sm">Download All</button>
                    <button id="download-zip" class="btn-primary text-sm">Download ZIP</button>
                  </div>
                </div>
              </div>
              <div id="results-list" class="divide-y divide-gray-200 dark:divide-gray-700"></div>
            </div>
          </div>
        </main>
      </div>
    `;
  }

  private setupFileUpload(): void {
    const dropZone = document.getElementById('drop-zone');
    if (!dropZone) return;

    this.fileUpload = new FileUpload(dropZone, {
      onFilesSelected: this.handleFilesSelected.bind(this),
      onError: this.handleFileErrors.bind(this),
      onDragStateChange: this.handleDragStateChange.bind(this),
      multiple: true,
      accept: 'image/*'
    });
  }

  private setupEventListeners(): void {
    // Theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    themeToggle?.addEventListener('click', this.toggleTheme.bind(this));

    // Format selection
    const formatSelect = document.getElementById('target-format') as HTMLSelectElement;
    formatSelect?.addEventListener('change', this.handleFormatChange.bind(this));

    // Quality slider
    const qualitySlider = document.getElementById('quality-slider') as HTMLInputElement;
    qualitySlider?.addEventListener('input', this.handleQualityChange.bind(this));

    // Convert button
    const convertBtn = document.getElementById('convert-btn');
    convertBtn?.addEventListener('click', this.handleConvert.bind(this));

    // Clear files
    const clearBtn = document.getElementById('clear-files');
    clearBtn?.addEventListener('click', this.handleClearFiles.bind(this));

    // Download buttons
    const downloadAllBtn = document.getElementById('download-all');
    downloadAllBtn?.addEventListener('click', this.handleDownloadAll.bind(this));

    const downloadZipBtn = document.getElementById('download-zip');
    downloadZipBtn?.addEventListener('click', this.handleDownloadZip.bind(this));
  }

  private handleFilesSelected(files: ImageFile[]): void {
    this.selectedFiles = [...this.selectedFiles, ...files];
    this.updateFilesDisplay();
    this.showSection('settings-section');
    this.showSection('files-section');
    this.updateConvertButton();
    
    toastManager.show(`${files.length} file(s) added successfully`, 'success');
  }

  private handleFileErrors(errors: ProcessingError[]): void {
    errors.forEach(error => {
      toastManager.show(error.message, 'error');
    });
  }

  private handleDragStateChange(isDragging: boolean): void {
    const dropZone = document.getElementById('drop-zone');
    if (dropZone) {
      if (isDragging) {
        dropZone.classList.add('drag-over');
      } else {
        dropZone.classList.remove('drag-over');
      }
    }
  }

  private handleFormatChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.conversionSettings.targetFormat = target.value as ImageFormat;
    
    // Update quality slider visibility
    const qualityContainer = document.getElementById('quality-slider')?.parentElement;
    if (qualityContainer) {
      const supportsQuality = target.value === ImageFormat.JPEG || target.value === ImageFormat.WEBP;
      qualityContainer.style.display = supportsQuality ? 'block' : 'none';
    }
  }

  private handleQualityChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const quality = parseInt(target.value) / 100;
    this.conversionSettings.quality = quality;
    
    const qualityValue = document.getElementById('quality-value');
    if (qualityValue) {
      qualityValue.textContent = `${target.value}%`;
    }
  }

  private async handleConvert(): Promise<void> {
    if (this.selectedFiles.length === 0) return;

    // Start performance monitoring
    this.performanceMonitor.startOperation('batch-conversion');

    const convertBtn = document.getElementById('convert-btn') as HTMLButtonElement;
    convertBtn.disabled = true;
    convertBtn.innerHTML = `
      <div class="flex items-center justify-center">
        <div class="spinner w-4 h-4 mr-2"></div>
        Converting...
      </div>
    `;

    try {
      // Check if system can handle the processing
      const totalSize = this.selectedFiles.reduce((sum, file) => sum + file.size, 0);
      const estimatedMemoryUsage = totalSize * 2; // Rough estimate

      if (!this.performanceMonitor.canHandleAdditionalProcessing(estimatedMemoryUsage)) {
        toastManager.show('System memory is low. Consider processing fewer files.', 'warning');
      }

      this.currentJob = await this.batchProcessor.startBatchJob(
        this.selectedFiles,
        this.conversionSettings
      );

      toastManager.show('Conversion started', 'info');
    } catch (error: any) {
      toastManager.show(error.message || 'Failed to start conversion', 'error');
      this.resetConvertButton();
      this.performanceMonitor.endOperation('batch-conversion');
    }
  }

  private handleJobProgress(job: BatchJob): void {
    // Update progress indicators in the files list
    this.updateFilesProgress(job);
  }

  private handleFileProgress(_progress: ProcessingProgress): void {
    // Individual file progress updates are handled in handleJobProgress
  }

  private handleJobComplete(job: BatchJob): void {
    this.currentJob = job;
    this.convertedResults = job.results;
    this.showSection('results-section');
    this.updateResultsDisplay(job.results);
    this.resetConvertButton();

    // End performance monitoring
    const conversionTime = this.performanceMonitor.endOperation('batch-conversion');
    console.log(`Batch conversion completed in ${conversionTime}ms`);

    const successCount = job.completedFiles;
    const failedCount = job.failedFiles;

    // Record performance metrics
    job.results.forEach(result => {
      this.performanceMonitor.recordFileProcessing(
        result.originalFile.size,
        result.conversionTime,
        true
      );
    });

    if (failedCount === 0) {
      toastManager.show(`All ${successCount} files converted successfully!`, 'success');
    } else {
      toastManager.show(`${successCount} files converted, ${failedCount} failed`, 'warning');
    }
  }

  private handleJobError(_job: BatchJob, error: ProcessingError): void {
    toastManager.show(error.message, 'error');
    this.resetConvertButton();
    this.performanceMonitor.endOperation('batch-conversion');
  }

  private handleClearFiles(): void {
    this.selectedFiles = [];
    this.currentJob = null; // Clear current job when files are cleared
    this.updateFilesDisplay();
    this.hideSection('settings-section');
    this.hideSection('files-section');
    this.hideSection('results-section');
    this.updateConvertButton();
  }

  /**
   * Gets the current batch job (for debugging/monitoring purposes)
   */
  getCurrentJob(): BatchJob | null {
    return this.currentJob;
  }

  private async handleDownloadAll(): Promise<void> {
    if (this.convertedResults.length === 0) {
      toastManager.show('No files to download', 'warning');
      return;
    }

    try {
      await this.downloadManager.downloadFiles(this.convertedResults);
    } catch (error) {
      toastManager.show('Download failed', 'error');
    }
  }

  private async handleDownloadZip(): Promise<void> {
    if (this.convertedResults.length === 0) {
      toastManager.show('No files to download', 'warning');
      return;
    }

    try {
      const zipFileName = this.downloadManager.generateZipFileName('converted-images');
      await this.downloadManager.downloadAsZip(this.convertedResults, zipFileName);
    } catch (error) {
      toastManager.show('ZIP download failed', 'error');
    }
  }

  private toggleTheme(): void {
    document.documentElement.classList.toggle('dark');
    localStorage.setItem('theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
  }

  private showSection(sectionId: string): void {
    const section = document.getElementById(sectionId);
    if (section) {
      section.classList.remove('hidden');
    }
  }

  private hideSection(sectionId: string): void {
    const section = document.getElementById(sectionId);
    if (section) {
      section.classList.add('hidden');
    }
  }

  private updateConvertButton(): void {
    const convertBtn = document.getElementById('convert-btn') as HTMLButtonElement;
    if (convertBtn) {
      convertBtn.disabled = this.selectedFiles.length === 0;
    }
  }

  private resetConvertButton(): void {
    const convertBtn = document.getElementById('convert-btn') as HTMLButtonElement;
    if (convertBtn) {
      convertBtn.disabled = this.selectedFiles.length === 0;
      convertBtn.innerHTML = 'Convert Images';
    }
  }

  private updateFilesDisplay(): void {
    const filesList = document.getElementById('files-list');
    if (!filesList) return;

    if (this.selectedFiles.length === 0) {
      filesList.innerHTML = '<div class="p-4 text-center text-gray-500 dark:text-gray-400">No files selected</div>';
      return;
    }

    filesList.innerHTML = this.selectedFiles.map((file, index) => `
      <div class="p-4 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
            ${file.preview ? `<img src="${file.preview}" alt="${file.name}" class="w-full h-full object-cover rounded-lg">` : '📄'}
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900 dark:text-white">${file.name}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">${formatFileSize(file.size)} • ${file.dimensions ? `${file.dimensions.width}×${file.dimensions.height}` : 'Unknown dimensions'}</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <div id="progress-${index}" class="hidden w-32"></div>
          <div id="status-${index}"></div>
          <button onclick="imageConverter.removeFile(${index})" class="text-gray-400 hover:text-red-500">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    `).join('');
  }

  private updateFilesProgress(job: BatchJob): void {
    job.progress.forEach((progress, index) => {
      const progressContainer = document.getElementById(`progress-${index}`);
      const statusContainer = document.getElementById(`status-${index}`);
      
      if (progressContainer && statusContainer) {
        if (progress.status === ProcessingStatus.PROCESSING) {
          progressContainer.classList.remove('hidden');
          progressContainer.innerHTML = '';
          const progressBar = createProgressBar(progress.progress);
          progressContainer.appendChild(progressBar);
        } else {
          progressContainer.classList.add('hidden');
        }
        
        statusContainer.innerHTML = '';
        statusContainer.appendChild(createStatusBadge(progress.status));
      }
    });
  }

  private updateResultsDisplay(results: ConvertedFile[]): void {
    const resultsList = document.getElementById('results-list');
    if (!resultsList) return;

    resultsList.innerHTML = results.map(result => `
      <div class="p-4 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
            📄
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900 dark:text-white">${result.convertedName}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              ${formatFileSize(result.convertedSize)} • 
              ${FORMAT_NAMES[result.convertedFormat]} • 
              ${result.conversionTime}ms • 
              ${(result.compressionRatio).toFixed(1)}x compression
            </p>
          </div>
        </div>
        <button onclick="imageConverter.downloadFile('${result.id}')" class="btn-secondary text-sm">
          Download
        </button>
      </div>
    `).join('');
  }

  // Public methods for global access
  public removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
    this.updateFilesDisplay();
    this.updateConvertButton();
    
    if (this.selectedFiles.length === 0) {
      this.hideSection('settings-section');
      this.hideSection('files-section');
    }
  }

  public async downloadFile(resultId: string): Promise<void> {
    const result = this.convertedResults.find(r => r.id === resultId);
    if (!result) {
      toastManager.show('File not found', 'error');
      return;
    }

    try {
      await this.downloadManager.downloadFile(result);
    } catch (error) {
      toastManager.show('Download failed', 'error');
    }
  }

  public destroy(): void {
    this.fileUpload?.destroy();
    this.batchProcessor.destroy();
  }
}

// Global instance for access from HTML
declare global {
  interface Window {
    imageConverter: ImageConverter;
  }
}
