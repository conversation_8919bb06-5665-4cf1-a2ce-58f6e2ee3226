import { 
  WorkerMessageType,
  ConvertImageMessage,
  ConversionProgressMessage,
  ConversionCompleteMessage,
  ConversionErrorMessage,
  ConversionSettings,
  ImageFormat,
  ProcessingError,
  ErrorType
} from '../types';

import { ERROR_MESSAGES } from '../constants';

// Worker context
const ctx: DedicatedWorkerGlobalScope = self as any;

class ImageWorkerProcessor {
  constructor() {
    // ImageProcessor initialization removed as it's not used
  }

  async processImage(message: ConvertImageMessage): Promise<void> {
    const { id, payload } = message;
    const { imageData, settings, fileName } = payload;

    try {
      // Send progress update
      this.sendProgress(id, 10, 'Starting conversion...');

      // Create a temporary canvas to work with ImageData
      const canvas = new OffscreenCanvas(imageData.width, imageData.height);
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('OffscreenCanvas context not available');
      }

      // Put image data on canvas
      ctx.putImageData(imageData, 0, 0);
      this.sendProgress(id, 30, 'Processing image...');

      // Convert canvas to blob with target format and quality
      const blob = await this.canvasToBlob(canvas, settings);
      this.sendProgress(id, 80, 'Finalizing conversion...');

      // Send completion message
      const completeMessage: ConversionCompleteMessage = {
        type: WorkerMessageType.CONVERSION_COMPLETE,
        id,
        payload: {
          blob,
          fileName: this.generateFileName(fileName, settings.targetFormat),
          conversionTime: Date.now() // This should be calculated properly
        }
      };

      self.postMessage(completeMessage);
      this.sendProgress(id, 100, 'Conversion complete');

    } catch (error) {
      this.sendError(id, error, fileName);
    }
  }

  private async canvasToBlob(
    canvas: OffscreenCanvas, 
    settings: ConversionSettings
  ): Promise<Blob> {
    const blob = await canvas.convertToBlob({
      type: settings.targetFormat,
      quality: settings.quality
    });

    if (!blob) {
      throw new Error('Failed to convert canvas to blob');
    }

    return blob;
  }

  private generateFileName(originalName: string, targetFormat: ImageFormat): string {
    const extensionMap: Record<ImageFormat, string> = {
      [ImageFormat.JPEG]: 'jpg',
      [ImageFormat.PNG]: 'png',
      [ImageFormat.WEBP]: 'webp',
      [ImageFormat.GIF]: 'gif',
      [ImageFormat.BMP]: 'bmp'
    };

    const lastDot = originalName.lastIndexOf('.');
    const baseName = lastDot !== -1 ? originalName.substring(0, lastDot) : originalName;
    const extension = extensionMap[targetFormat];
    
    return `${baseName}.${extension}`;
  }

  private sendProgress(id: string, progress: number, message?: string): void {
    const progressMessage: ConversionProgressMessage = {
      type: WorkerMessageType.CONVERSION_PROGRESS,
      id,
      payload: {
        progress,
        message
      }
    };

    ctx.postMessage(progressMessage);
  }

  private sendError(id: string, error: any, fileName?: string): void {
    const processingError: ProcessingError = {
      type: ErrorType.PROCESSING_ERROR,
      message: error.message || ERROR_MESSAGES.PROCESSING_ERROR,
      fileName,
      details: error,
      timestamp: Date.now()
    };

    const errorMessage: ConversionErrorMessage = {
      type: WorkerMessageType.CONVERSION_ERROR,
      id,
      payload: {
        error: processingError
      }
    };

    ctx.postMessage(errorMessage);
  }
}

// Initialize worker processor
const workerProcessor = new ImageWorkerProcessor();

// Handle messages from main thread
ctx.addEventListener('message', async (event) => {
  const message = event.data;

  switch (message.type) {
    case WorkerMessageType.CONVERT_IMAGE:
      await workerProcessor.processImage(message as ConvertImageMessage);
      break;
    
    default:
      console.warn('Unknown message type:', message.type);
  }
});

// Handle worker errors
ctx.addEventListener('error', (error) => {
  console.error('Worker error:', error);
});

// Handle unhandled promise rejections
ctx.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in worker:', event.reason);
});

export {}; // Make this a module
