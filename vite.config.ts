import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [tailwindcss()],
  build: {
    target: 'es2020',
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          'image-processing': ['./src/utils/imageProcessor.ts'],
          'file-utils': ['./src/utils/fileUtils.ts'],
        }
      }
    }
  },
  server: {
    port: 3000,
    open: true
  },
  worker: {
    format: 'es'
  }
})
