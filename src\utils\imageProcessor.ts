import { 
  ImageFile, 
  ConversionSettings, 
  ConvertedFile, 
  ImageFormat,
  ProcessingError,
  ErrorType,
  CanvasOptions 
} from '../types';
import { 
  FILE_EXTENSIONS, 
  DEFAULT_QUALITY, 
  CANVAS_SETTINGS,
  ERROR_MESSAGES 
} from '../constants';
import { changeFileExtension, generateFileId } from './fileUtils';

export class ImageProcessor {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    this.canvas = document.createElement('canvas');
    const ctx = this.canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Canvas 2D context not supported');
    }
    this.ctx = ctx;
  }

  /**
   * Converts an image file to the specified format
   */
  async convertImage(
    imageFile: ImageFile,
    settings: ConversionSettings
  ): Promise<ConvertedFile> {
    const startTime = Date.now();

    try {
      // Load image
      const img = await this.loadImage(imageFile.file);

      // Setup canvas with proper dimensions
      const dimensions = this.calculateDimensions(img, settings);
      this.setupCanvas(dimensions, {
        smoothing: CANVAS_SETTINGS.DEFAULT_SMOOTHING,
        alpha: this.needsAlpha(settings.targetFormat),
        colorSpace: CANVAS_SETTINGS.DEFAULT_COLOR_SPACE
      });

      // Draw image to canvas
      this.drawImage(img, dimensions);

      // Convert to target format
      const blob = await this.canvasToBlob(settings.targetFormat, settings.quality);

      // Generate result
      const convertedName = this.generateConvertedFileName(
        imageFile.name,
        settings.targetFormat
      );

      const conversionTime = Date.now() - startTime;
      const compressionRatio = imageFile.size / blob.size;

      return {
        id: generateFileId(),
        originalFile: imageFile,
        convertedBlob: blob,
        convertedName,
        convertedSize: blob.size,
        convertedFormat: settings.targetFormat,
        conversionTime,
        compressionRatio
      };

    } catch (error) {
      throw this.createProcessingError(error, imageFile);
    }
  }

  /**
   * Loads an image file into an Image element
   */
  private loadImage(file: File): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve(img);
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };

      img.src = url;
    });
  }

  /**
   * Calculates target dimensions based on settings
   */
  private calculateDimensions(
    img: HTMLImageElement,
    settings: ConversionSettings
  ): { width: number; height: number } {
    if (settings.customDimensions && !settings.maintainDimensions) {
      return settings.customDimensions;
    }

    return {
      width: img.naturalWidth,
      height: img.naturalHeight
    };
  }

  /**
   * Sets up canvas with specified dimensions and options
   */
  private setupCanvas(
    dimensions: { width: number; height: number },
    options: CanvasOptions
  ): void {
    // Check canvas size limits
    if (dimensions.width > CANVAS_SETTINGS.MAX_CANVAS_SIZE || 
        dimensions.height > CANVAS_SETTINGS.MAX_CANVAS_SIZE) {
      throw new Error('Image dimensions exceed canvas size limits');
    }

    this.canvas.width = dimensions.width;
    this.canvas.height = dimensions.height;

    // Configure context
    this.ctx.imageSmoothingEnabled = options.smoothing;
    if (this.ctx.imageSmoothingQuality) {
      this.ctx.imageSmoothingQuality = 'high';
    }

    // Clear canvas
    this.ctx.clearRect(0, 0, dimensions.width, dimensions.height);

    // Set background for formats that don't support transparency
    if (!options.alpha) {
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.fillRect(0, 0, dimensions.width, dimensions.height);
    }
  }

  /**
   * Draws image to canvas with proper scaling
   */
  private drawImage(
    img: HTMLImageElement,
    dimensions: { width: number; height: number }
  ): void {
    this.ctx.drawImage(
      img,
      0, 0, img.naturalWidth, img.naturalHeight,
      0, 0, dimensions.width, dimensions.height
    );
  }

  /**
   * Converts canvas to blob with specified format and quality
   */
  private canvasToBlob(format: ImageFormat, quality: number): Promise<Blob> {
    return new Promise((resolve, reject) => {
      this.canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert canvas to blob'));
          }
        },
        format,
        quality
      );
    });
  }

  /**
   * Determines if format needs alpha channel
   */
  private needsAlpha(format: ImageFormat): boolean {
    return format === ImageFormat.PNG || format === ImageFormat.WEBP;
  }

  /**
   * Generates converted file name
   */
  private generateConvertedFileName(originalName: string, targetFormat: ImageFormat): string {
    const extension = FILE_EXTENSIONS[targetFormat];
    return changeFileExtension(originalName, extension);
  }

  /**
   * Creates a processing error from caught exception
   */
  private createProcessingError(error: any, imageFile: ImageFile): ProcessingError {
    let errorType = ErrorType.PROCESSING_ERROR;
    let message: string = ERROR_MESSAGES.PROCESSING_ERROR;

    if (error.message?.includes('memory') || error.message?.includes('Memory')) {
      errorType = ErrorType.MEMORY_ERROR;
      message = ERROR_MESSAGES.MEMORY_ERROR;
    }

    return {
      type: errorType,
      message,
      fileId: imageFile.id,
      fileName: imageFile.name,
      details: error.message || error,
      timestamp: Date.now()
    };
  }

  /**
   * Gets optimal quality for format
   */
  static getOptimalQuality(format: ImageFormat, customQuality?: number): number {
    if (customQuality !== undefined) {
      return Math.max(0.1, Math.min(1.0, customQuality));
    }

    return DEFAULT_QUALITY[format.split('/')[1] as keyof typeof DEFAULT_QUALITY] || 0.9;
  }

  /**
   * Checks if format supports quality setting
   */
  static supportsQuality(format: ImageFormat): boolean {
    return format === ImageFormat.JPEG || format === ImageFormat.WEBP;
  }

  /**
   * Gets canvas memory usage estimate
   */
  static estimateMemoryUsage(width: number, height: number): number {
    // 4 bytes per pixel (RGBA)
    return width * height * 4;
  }

  /**
   * Checks if image dimensions are safe for processing
   */
  static isSafeDimensions(width: number, height: number): boolean {
    return width <= CANVAS_SETTINGS.MAX_CANVAS_SIZE && 
           height <= CANVAS_SETTINGS.MAX_CANVAS_SIZE &&
           this.estimateMemoryUsage(width, height) < 500 * 1024 * 1024; // 500MB limit
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    // Canvas cleanup is handled by garbage collector
    // but we can clear the context
    if (this.ctx) {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
  }
}

/**
 * Utility function to create image processor instance
 */
export function createImageProcessor(): ImageProcessor {
  return new ImageProcessor();
}

/**
 * Batch process multiple images
 */
export async function batchProcessImages(
  imageFiles: ImageFile[],
  settings: ConversionSettings,
  onProgress?: (progress: number, fileIndex: number) => void
): Promise<ConvertedFile[]> {
  const processor = createImageProcessor();
  const results: ConvertedFile[] = [];

  try {
    for (let i = 0; i < imageFiles.length; i++) {
      const imageFile = imageFiles[i];

      if (onProgress) {
        onProgress((i / imageFiles.length) * 100, i);
      }

      if (imageFile) {
        const result = await processor.convertImage(imageFile, settings);
        results.push(result);
      }
    }

    if (onProgress) {
      onProgress(100, imageFiles.length);
    }

    return results;
  } finally {
    processor.destroy();
  }
}
